<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/assess/logoss/logoss/logoss/logots/logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام أهلنا للكاشير</title>
    <meta name="description" content="نظام نقاط البيع الاحترافي لمطعم أهلنا" />

    <!-- خطوط عربية من Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', Arial, sans-serif;
        direction: rtl;
        background-color: #f8f9fa;
      }

      #app {
        height: 100vh;
        width: 100vw;
      }

      /* Loading spinner */
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-size: 18px;
        color: #1976d2;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-b7ffe049.js"></script>
  </head>
  <body>
    <div id="app">
      <div class="loading">
        جاري تحميل النظام...
      </div>
    </div>
    
  </body>
</html>
