const { app, BrowserWindow } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

let mainWindow;

function createWindow() {
  // إنشاء نافذة المتصفح الرئيسية
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
    },
    title: 'نظام أهلنا للكاشير',
    show: false,
  });

  // تحميل التطبيق
  const startUrl = isDev
    ? 'http://localhost:5173'
    : `file://${path.join(__dirname, 'app.html')}`;

  mainWindow.loadURL(startUrl);

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // فتح أدوات المطور في وضع التطوير
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // إغلاق التطبيق عند إغلاق النافذة الرئيسية
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// هذا الحدث سيتم استدعاؤه عندما يكون Electron جاهزاً
app.whenReady().then(createWindow);

// إنهاء التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  app.quit();
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});


