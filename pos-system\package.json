{"name": "pos-system", "private": true, "version": "1.0.0", "type": "module", "main": "public/electron.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.0.3", "concurrently": "^9.1.2", "electron": "^36.4.0", "electron-builder": "^26.0.12", "vite": "^4.4.5", "wait-on": "^8.0.3"}, "build": {"appId": "com.ahlna.pos", "productName": "نظام أهلنا للكاشير", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "public/electron.js", "!node_modules"], "win": {"target": "nsis", "icon": "public/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}