import React, { useState } from 'react';
import './styles/global.css';

// بيانات المنتجات المبسطة
const products = [
  { id: 1, name: 'قهوة عربية', price: 15, category: 'مشروبات ساخنة', emoji: '☕' },
  { id: 2, name: 'شاي أحمر', price: 10, category: 'مشروبات ساخنة', emoji: '🍵' },
  { id: 3, name: 'عصير برتقال', price: 18, category: 'مشروبات باردة', emoji: '🍊' },
  { id: 4, name: 'برجر لحم', price: 45, category: 'وجبات رئيسية', emoji: '🍔' },
  { id: 5, name: 'دجاج مشوي', price: 55, category: 'وجبات رئيسية', emoji: '🍗' },
  { id: 6, name: 'سلطة فتوش', price: 18, category: 'مقبلات', emoji: '🥗' },
  { id: 7, name: 'كنافة', price: 25, category: 'حلويات', emoji: '🍰' },
  { id: 8, name: 'شاورما دجاج', price: 25, category: 'سندويشات', emoji: '🌯' },
];

function App() {
  const [currentPage, setCurrentPage] = useState('cashier');
  const [cartItems, setCartItems] = useState([]);
  const [orderModalOpen, setOrderModalOpen] = useState(false);

  const addToCart = (product) => {
    const existingItem = cartItems.find(item => item.id === product.id);
    if (existingItem) {
      setCartItems(cartItems.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCartItems([...cartItems, { ...product, quantity: 1 }]);
    }
  };

  const removeFromCart = (productId) => {
    setCartItems(cartItems.filter(item => item.id !== productId));
  };

  const updateQuantity = (productId, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
    } else {
      setCartItems(cartItems.map(item =>
        item.id === productId
          ? { ...item, quantity: newQuantity }
          : item
      ));
    }
  };

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
          <Box sx={{ display: 'flex', height: '100vh', direction: 'rtl' }}>
            {/* الشريط الجانبي */}
            <Sidebar
              open={true}
              currentPage={currentPage}
              onPageChange={handlePageChange}
              darkMode={darkMode}
              onToggleDarkMode={handleToggleDarkMode}
            />

            {/* المحتوى الرئيسي */}
            <Box
              component="main"
              sx={{
                flexGrow: 1,
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
                backgroundColor: 'background.default',
              }}
            >
              {/* الشريط العلوي */}
              <Header
                title={getPageTitle()}
                cartItemsCount={cartItemsCount}
                onCartClick={handleCartClick}
                onPrintClick={handlePrintClick}
                onRefreshClick={handleRefreshClick}
              />

              {/* محتوى الصفحة */}
              <Box
                sx={{
                  flex: 1,
                  overflow: 'auto',
                  mt: 8, // ارتفاع الشريط العلوي
                }}
              >
                {renderCurrentPage()}
              </Box>
            </Box>
          </Box>
        </Router>
      </ThemeProvider>
  );
}

export default App;

