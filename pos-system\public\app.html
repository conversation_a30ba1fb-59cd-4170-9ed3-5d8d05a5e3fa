<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام أهلنا للكاشير</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2rem;
        }
        
        .status {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-weight: bold;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        
        .feature-desc {
            color: #666;
            font-size: 0.9rem;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .instructions h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            text-align: right;
            color: #856404;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .version {
            margin-top: 30px;
            color: #999;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏪</div>
        <h1>نظام أهلنا للكاشير</h1>
        <p class="subtitle">نظام نقاط البيع الاحترافي</p>
        
        <div class="status">
            ✅ تم تشغيل التطبيق بنجاح!
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">💰</div>
                <div class="feature-title">نظام الكاشير</div>
                <div class="feature-desc">إدارة المبيعات والمنتجات</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🚚</div>
                <div class="feature-title">إدارة التوصيل</div>
                <div class="feature-desc">تتبع طلبات التوصيل</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div class="feature-title">التقارير</div>
                <div class="feature-desc">إحصائيات المبيعات</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">⚙️</div>
                <div class="feature-title">الإعدادات</div>
                <div class="feature-desc">تخصيص النظام</div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <ol>
                <li>هذا التطبيق يعمل كتطبيق سطح مكتب مستقل</li>
                <li>يمكن نسخه وتوزيعه على أجهزة أخرى</li>
                <li>لا يحتاج اتصال بالإنترنت للعمل</li>
                <li>جميع البيانات محفوظة محلياً</li>
            </ol>
        </div>
        
        <div class="version">
            الإصدار 1.0 - تم التطوير بواسطة Augment Agent
        </div>
    </div>
    
    <script>
        // إضافة بعض التفاعل
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الحركة للعناصر
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    feature.style.transition = 'all 0.5s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateY(0)';
                }, index * 100);
            });
            
            // تحديث الوقت
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA');
                const dateString = now.toLocaleDateString('ar-SA');
                
                // يمكن إضافة عرض الوقت هنا إذا أردت
            }
            
            updateTime();
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>
