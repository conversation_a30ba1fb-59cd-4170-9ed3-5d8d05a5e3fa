const { app, BrowserWindow, Menu } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

let mainWindow;

function createWindow() {
  // إنشاء نافذة المتصفح الرئيسية
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
    },
    icon: path.join(__dirname, 'icon.png'),
    titleBarStyle: 'default',
    show: false,
  });

  // تحميل التطبيق
  const startUrl = isDev 
    ? 'http://localhost:5173' 
    : `file://${path.join(__dirname, '../dist/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // فتح أدوات المطور في وضع التطوير
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // إغلاق التطبيق عند إغلاق النافذة الرئيسية
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// هذا الحدث سيتم استدعاؤه عندما يكون Electron جاهزاً
app.whenReady().then(createWindow);

// إنهاء التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// إعداد القائمة العربية
const template = [
  {
    label: 'ملف',
    submenu: [
      {
        label: 'طلب جديد',
        accelerator: 'CmdOrCtrl+N',
        click: () => {
          mainWindow.webContents.send('new-order');
        }
      },
      {
        label: 'طباعة',
        accelerator: 'CmdOrCtrl+P',
        click: () => {
          mainWindow.webContents.send('print-receipt');
        }
      },
      { type: 'separator' },
      {
        label: 'خروج',
        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
        click: () => {
          app.quit();
        }
      }
    ]
  },
  {
    label: 'عرض',
    submenu: [
      {
        label: 'إعادة تحميل',
        accelerator: 'CmdOrCtrl+R',
        click: () => {
          mainWindow.reload();
        }
      },
      {
        label: 'ملء الشاشة',
        accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
        click: () => {
          mainWindow.setFullScreen(!mainWindow.isFullScreen());
        }
      }
    ]
  },
  {
    label: 'مساعدة',
    submenu: [
      {
        label: 'حول البرنامج',
        click: () => {
          // يمكن إضافة نافذة معلومات هنا
        }
      }
    ]
  }
];

const menu = Menu.buildFromTemplate(template);
Menu.setApplicationMenu(menu);

// معالجة أحداث IPC
ipcMain.handle('print-receipt', async (event, receiptData) => {
  // منطق طباعة الفاتورة
  console.log('طباعة الفاتورة:', receiptData);
  return { success: true };
});

ipcMain.handle('save-order', async (event, orderData) => {
  // منطق حفظ الطلب في قاعدة البيانات
  console.log('حفظ الطلب:', orderData);
  return { success: true, orderId: Date.now() };
});
